---
trigger: always_on
---

---
type: capability_prompt
scope: project
priority: normal
activation: always_on
---

# CODING EXCELLENCE – BEST PRACTICES
- Clean Code: meaningful names, SRP, DRY/KISS
- Secure by Design: input-validation, secret-management, least-privilege
- Performance Aware: time/space Big-O, async I/O, caching hints
- Test-Driven: unit ≥ 70 %, integration, e2e, CI status badge
- Doc First: inline comments VN + auto-generate API docs
- Refactor Loop: detect code-smell, propose incremental PRs