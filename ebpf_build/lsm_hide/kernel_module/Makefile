# Makefile for proc_hide_fallback kernel module
# Compatible with kernel 6.8.0-1026-azure

obj-m := proc_hide_fallback.o

# Kernel build directory
KDIR ?= /lib/modules/$(shell uname -r)/build

# Module source directory
PWD := $(shell pwd)

# Compiler flags for kernel module
ccflags-y := -Wall -Wextra -O2

# Build targets
all:
	$(MAKE) -C $(KDIR) M=$(PWD) modules

clean:
	$(MAKE) -C $(KDIR) M=$(PWD) clean
	rm -f *.o *.ko *.mod.c *.mod *.order *.symvers

install: all
	$(MAKE) -C $(KDIR) M=$(PWD) modules_install
	depmod -a

load: all
	sudo insmod proc_hide_fallback.ko

unload:
	sudo rmmod proc_hide_fallback

reload: unload load

# Check module info
info: all
	modinfo proc_hide_fallback.ko

# Test module size (should be < 15 KiB)
size: all
	@echo "Module size check:"
	@ls -lh proc_hide_fallback.ko | awk '{print $$5 " " $$9}'
	@size proc_hide_fallback.ko

# Development targets
dev-load: all
	sudo dmesg -C
	sudo insmod proc_hide_fallback.ko
	dmesg | tail -20

dev-unload:
	sudo rmmod proc_hide_fallback
	dmesg | tail -10

# Check kernel compatibility
check-kernel:
	@echo "Current kernel: $(shell uname -r)"
	@echo "Target kernel: 6.8.0-1026-azure"
	@if [ "$(shell uname -r)" = "6.8.0-1026-azure" ]; then \
		echo "✓ Kernel version matches target"; \
	else \
		echo "⚠ Kernel version differs from target"; \
	fi

.PHONY: all clean install load unload reload info size dev-load dev-unload check-kernel
