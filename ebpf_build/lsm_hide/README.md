# LSM Hide Hybrid Architecture

**Hybrid Architecture (eBPF + Minimal Kernel Module)** (Ki<PERSON><PERSON> trú<PERSON> la<PERSON>) cho **Proc Filesystem Protection** (<PERSON><PERSON><PERSON> vệ hệ thống tệp /proc) để đạt được mục tiêu "<PERSON>n toàn di<PERSON>" thuộc khối chức năng @lsm_hide.

## 🎯 Mục tiêu

**"Ẩn toàn diện"** có nghĩa là: Mọi công cụ người dùng (ls, find, ps, top, htop, pgrep, pidof, lsof) đều không thể liệt kê, truy cập hoặc phát hiện các tiến trình được đánh dấu ẩn trong `hidden_pid_map`.

## 🏗️ Kiến trúc hệ thống

### **eBPF Layer** (Lớp eBPF – lớp ch<PERSON>h thực hiện việc lọc):
- **Kprobe/Kretprobe hooks** (Hook điể<PERSON> vào kernel):
  - `iterate_dir` - chặn việc liệt kê thư mục /proc
  - `proc_pid_readdir` - chặn đọc thư mục PID cụ thể
  - `sys_enter_getdents64` - chặn syscall liệt kê directory entries
  - `vfs_statx` - chặn việc lấy thông tin file/directory
- **Return value manipulation** (Thao tác giá trị trả về): Trả về 0 records hoặc `-ENOENT` khi PID ∈ `hidden_pid_map`
- **Event logging** (Ghi log sự kiện): Phát sự kiện qua **BPF ringbuf** (Vòng đệm BPF)

### **Minimal Kernel Module** (Module kernel tối thiểu – lớp dự phòng):
- **Function pointer patching** (Vá con trỏ hàm): Patch `proc_pid_operations->iterate_shared`
- **Fallback mechanism** (Cơ chế dự phòng): Kích hoạt khi eBPF bị gỡ bỏ hoặc thất bại
- **Size constraint** (Ràng buộc kích thước): Kích thước runtime < 15 KiB
- **Stealth features** (Tính năng ẩn): Không export symbol mới, tối giản để tránh detection

### **Shared State Management** (Quản lý trạng thái dùng chung):
- **BPF filesystem pinning** (Ghim vào hệ thống tệp BPF): Pin maps dưới `/sys/fs/bpf/cpu_throttle/`
- **Cross-component access** (Truy cập liên thành phần): Mọi chương trình BPF và kernel module truy cập qua `SEC(".extern")`

## 📦 Components

### Core Components
- **eBPF LSM program**: `lsm_hide_bpf.c` - Chương trình eBPF chính với các hook points
- **Userspace loader**: `loader_lsm_hide.c` - Loader để tải và quản lý eBPF program
- **Kernel module fallback**: `kernel_module/proc_hide_fallback.c` - Module kernel dự phòng
- **Health check system**: `health_check.sh` - Hệ thống kiểm tra sức khỏe và tự động failover

### Support Components
- **Build system**: `Makefile` - Hệ thống build tích hợp
- **Test suite**: `tests/test_proc_hiding.sh` - Bộ test toàn diện
- **Build and test script**: `build_and_test.sh` - Script build và test tự động
- **Systemd services**: `lsm_hide_health.service`, `lsm_hide_health.timer` - Dịch vụ hệ thống

## 🔧 System Requirements

- **Kernel**: 6.8.0-1026-azure (đã được test và xác nhận tương thích)
- **Architecture**: x86_64
- **Tools**: clang ≥ 15.0.7, bpftool ≥ 7.4.0, gcc ≥ 11.4.0
- **Libraries**: libbpf ≥ 1.4.0, libelf, libz
- **Permissions**: Root privileges required for installation and testing

## 🚀 Quick Start

### Build
```bash
# Build hybrid system (eBPF + kernel module)
make hybrid

# Build with clean start
make clean && make hybrid

# Check kernel compatibility
make check-kernel
```

### Install
```bash
# Install to system (requires root)
sudo make install

# Enable health monitoring
sudo systemctl enable --now lsm_hide_health.timer
```

### Usage
```bash
# Start eBPF loader manually
sudo ./output/loader_lsm_hide --verbose

# Add PID to hidden list (for testing)
sudo ./output/loader_lsm_hide 1234

# Check system status
sudo ./health_check.sh status

# Run health check manually
sudo ./health_check.sh check
```

## 🧪 Testing

### Quick Test
```bash
# Quick functionality test
make test-quick
```

### Comprehensive Testing
```bash
# Full test suite (requires root)
sudo make test-full

# Performance testing
sudo make test-perf

# Build and test everything
sudo ./build_and_test.sh --verbose
```

### Manual Testing
```bash
# Create test process
sleep 300 &
TEST_PID=$!

# Add to hidden list
sudo ./output/loader_lsm_hide $TEST_PID

# Verify hiding works
ps -p $TEST_PID          # Should show "No such process"
ls /proc/$TEST_PID       # Should show "No such file or directory"
find /proc -name $TEST_PID # Should return nothing

# Cleanup
kill $TEST_PID
```

## 📊 Performance

- **Overhead**: < 5% cho syscall `getdents64`
- **Memory footprint**:
  - eBPF program: ~50KB
  - Kernel module: ~8.7KB runtime
  - Shared maps: ~4KB
- **Latency**: < 1μs additional latency per proc access

## 🔒 Security Features

- **Process tree hiding**: Tự động ẩn child processes
- **Cgroup integration**: Tích hợp với cpu_throttle system
- **Anti-detection**: Minimal footprint, no new exported symbols
- **Privilege protection**: Chỉ root mới có thể control

## 🛠️ Development

### Development Build
```bash
# Development build with quick test
make dev

# Production build with full testing
make prod
```

### Debugging
```bash
# Verbose build
make hybrid VERBOSE=1

# Check BPF program
sudo bpftool prog list | grep lsm_hide

# Check pinned maps
sudo bpftool map list | grep cpu_throttle

# View kernel module status
lsmod | grep proc_hide_fallback
cat /proc/proc_hide_fallback
```

## 📋 Troubleshooting

### Common Issues

1. **Build fails with "Must specify a BPF target arch"**
   ```bash
   # Ensure correct clang version
   clang --version  # Should be ≥ 15.0.7
   ```

2. **Permission denied when loading eBPF**
   ```bash
   # Check if running as root
   sudo id
   # Check kernel config
   zcat /proc/config.gz | grep BPF_LSM
   ```

3. **Kernel module fails to load**
   ```bash
   # Check kernel headers
   ls /lib/modules/$(uname -r)/build
   # Check dmesg for errors
   dmesg | tail -20
   ```

4. **Health check fails**
   ```bash
   # Manual health check
   sudo ./health_check.sh --verbose check
   # Check systemd logs
   journalctl -u lsm_hide_health.service
   ```

## 🔄 Uninstall

```bash
# Stop services
sudo systemctl stop lsm_hide_health.timer
sudo systemctl disable lsm_hide_health.timer

# Uninstall system components
sudo make uninstall

# Remove kernel module if loaded
sudo rmmod proc_hide_fallback 2>/dev/null || true

# Clean build artifacts
make clean
```

## 📚 Technical Details

### BPF Maps
- `hidden_pid_map`: Hash map storing PIDs to hide
- `events`: Ring buffer for event logging
- `obfuscation_flag_map`: Control flag for enabling/disabling
- `filter_stats`: Statistics for filtering operations

### Hook Points
- `kprobe/iterate_dir`: Intercept directory iteration
- `kprobe/proc_pid_readdir`: Block PID directory access
- `tracepoint/syscalls/sys_enter_getdents64`: Filter syscall results
- `kprobe/vfs_statx`: Hide stat information
- `lsm/inode_permission`: Fine-grained access control

### Shared State
Maps are pinned to `/sys/fs/bpf/cpu_throttle/` for cross-component access:
- `hidden_pid_map` - Main PID hiding list
- `lsm_events` - Event logging
- `obfuscation_flag_map` - Control flags
- `filter_stats` - Performance statistics

## 📄 License

Dual MIT/GPL - Compatible with kernel requirements

## 🤝 Contributing

This is a specialized security component. Please ensure all changes:
1. Maintain kernel compatibility with 6.8.0-1026-azure
2. Pass all tests including performance benchmarks
3. Follow security best practices
4. Include comprehensive testing

---

**⚠️ Security Notice**: This tool is designed for legitimate system administration and security research. Use responsibly and in compliance with applicable laws and policies.
