#!/bin/bash
#
# build_and_test.sh - Build and Test LSM Hide System
#
# This script builds all components and runs comprehensive tests
# for the Hybrid Architecture (eBPF + Minimal Kernel Module)
#
# Compatible with kernel 6.8.0-1026-azure

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_LOG="/tmp/lsm_hide_build.log"
VERBOSE=false
CLEAN_BUILD=false
RUN_TESTS=true
INSTALL_SYSTEM=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$BUILD_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*" | tee -a "$BUILD_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*" | tee -a "$BUILD_LOG"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$BUILD_LOG"
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check kernel version
    local kernel_version=$(uname -r)
    log_info "Kernel version: $kernel_version"
    
    if [[ "$kernel_version" != "6.8.0-1026-azure" ]]; then
        log_warn "Kernel version differs from target (6.8.0-1026-azure)"
    fi
    
    # Check required tools
    local required_tools=("clang" "bpftool" "gcc" "make")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=$($tool --version | head -n1)
            log_info "$tool: $version"
        else
            log_error "Required tool '$tool' not found"
            return 1
        fi
    done
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log_info "Running as root"
    else
        log_warn "Not running as root - some operations may fail"
    fi
    
    # Check vmlinux.h
    if [[ -f "$SCRIPT_DIR/vmlinux.h" ]]; then
        log_info "vmlinux.h found"
    else
        log_warn "vmlinux.h not found, will generate"
    fi
    
    log_success "System requirements check completed"
}

# Generate vmlinux.h if needed
generate_vmlinux() {
    if [[ ! -f "$SCRIPT_DIR/vmlinux.h" ]]; then
        log_info "Generating vmlinux.h..."
        if bpftool btf dump file /sys/kernel/btf/vmlinux format c > "$SCRIPT_DIR/vmlinux.h"; then
            log_success "vmlinux.h generated successfully"
        else
            log_error "Failed to generate vmlinux.h"
            return 1
        fi
    else
        log_info "vmlinux.h already exists"
    fi
}

# Build eBPF components
build_ebpf() {
    log_info "Building eBPF components..."
    
    cd "$SCRIPT_DIR"
    
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        log_info "Cleaning previous build..."
        make clean
    fi
    
    # Build main eBPF program and loader
    if make main; then
        log_success "eBPF components built successfully"
    else
        log_error "Failed to build eBPF components"
        return 1
    fi
    
    # Check if binary exists and is executable
    if [[ -x "output/loader_lsm_hide" ]]; then
        log_info "eBPF loader binary created: $(ls -lh output/loader_lsm_hide)"
    else
        log_error "eBPF loader binary not found or not executable"
        return 1
    fi
}

# Build kernel module
build_kernel_module() {
    log_info "Building kernel module..."
    
    cd "$SCRIPT_DIR/kernel_module"
    
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        log_info "Cleaning kernel module..."
        make clean
    fi
    
    if make; then
        log_success "Kernel module built successfully"
    else
        log_error "Failed to build kernel module"
        return 1
    fi
    
    # Check module size
    if [[ -f "proc_hide_fallback.ko" ]]; then
        local module_size=$(stat -c%s "proc_hide_fallback.ko")
        local module_size_kb=$((module_size / 1024))
        log_info "Kernel module size: ${module_size_kb} KiB"
        
        if [[ $module_size_kb -lt 15 ]]; then
            log_success "Module size within 15 KiB limit"
        else
            log_warn "Module size exceeds 15 KiB limit"
        fi
    else
        log_error "Kernel module not found"
        return 1
    fi
}

# Install system components
install_system() {
    log_info "Installing system components..."
    
    # Install eBPF loader
    if install -m 755 "$SCRIPT_DIR/output/loader_lsm_hide" /usr/local/bin/; then
        log_success "eBPF loader installed to /usr/local/bin/"
    else
        log_error "Failed to install eBPF loader"
        return 1
    fi
    
    # Install health check script
    if install -m 755 "$SCRIPT_DIR/health_check.sh" /usr/local/bin/lsm_hide_health_check.sh; then
        log_success "Health check script installed"
    else
        log_error "Failed to install health check script"
        return 1
    fi
    
    # Install systemd services
    if install -m 644 "$SCRIPT_DIR/lsm_hide_health.service" /etc/systemd/system/ && \
       install -m 644 "$SCRIPT_DIR/lsm_hide_health.timer" /etc/systemd/system/; then
        log_success "Systemd services installed"
        
        # Reload systemd
        systemctl daemon-reload
        log_info "Systemd daemon reloaded"
    else
        log_error "Failed to install systemd services"
        return 1
    fi
    
    log_success "System installation completed"
}

# Run tests
run_tests() {
    log_info "Running test suite..."
    
    # Make test script executable
    chmod +x "$SCRIPT_DIR/tests/test_proc_hiding.sh"
    
    # Run tests
    if "$SCRIPT_DIR/tests/test_proc_hiding.sh"; then
        log_success "All tests passed"
    else
        log_error "Some tests failed"
        return 1
    fi
}

# Show build summary
show_summary() {
    log_info "=== Build Summary ==="
    
    # eBPF components
    if [[ -x "$SCRIPT_DIR/output/loader_lsm_hide" ]]; then
        log_success "✓ eBPF loader: $(ls -lh "$SCRIPT_DIR/output/loader_lsm_hide" | awk '{print $5}')"
    else
        log_error "✗ eBPF loader: Not found"
    fi
    
    # Kernel module
    if [[ -f "$SCRIPT_DIR/kernel_module/proc_hide_fallback.ko" ]]; then
        local size=$(stat -c%s "$SCRIPT_DIR/kernel_module/proc_hide_fallback.ko")
        log_success "✓ Kernel module: $((size / 1024)) KiB"
    else
        log_error "✗ Kernel module: Not found"
    fi
    
    # Health check
    if [[ -x "$SCRIPT_DIR/health_check.sh" ]]; then
        log_success "✓ Health check script: Available"
    else
        log_error "✗ Health check script: Not found"
    fi
    
    # Test suite
    if [[ -x "$SCRIPT_DIR/tests/test_proc_hiding.sh" ]]; then
        log_success "✓ Test suite: Available"
    else
        log_error "✗ Test suite: Not found"
    fi
    
    log_info "=== Build Complete ==="
}

# Usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    -v, --verbose       Enable verbose output
    -c, --clean         Clean build (remove previous artifacts)
    -t, --no-tests      Skip running tests
    -i, --install       Install system components
    -h, --help          Show this help message

Examples:
    $0                  # Standard build and test
    $0 -c -v            # Clean verbose build
    $0 --install        # Build and install to system
    $0 --no-tests       # Build without running tests

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -t|--no-tests)
            RUN_TESTS=false
            shift
            ;;
        -i|--install)
            INSTALL_SYSTEM=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "LSM Hide Build and Test - $(date)" > "$BUILD_LOG"
    
    log_info "=== LSM Hide Hybrid Architecture Build ==="
    log_info "Starting build process..."
    
    # Check requirements
    check_requirements || exit 1
    
    # Generate vmlinux.h
    generate_vmlinux || exit 1
    
    # Build components
    build_ebpf || exit 1
    build_kernel_module || exit 1
    
    # Install if requested
    if [[ "$INSTALL_SYSTEM" == "true" ]]; then
        if [[ $EUID -ne 0 ]]; then
            log_error "Installation requires root privileges"
            exit 1
        fi
        install_system || exit 1
    fi
    
    # Run tests
    if [[ "$RUN_TESTS" == "true" ]]; then
        if [[ $EUID -ne 0 ]]; then
            log_warn "Tests require root privileges, skipping"
        else
            run_tests || exit 1
        fi
    fi
    
    # Show summary
    show_summary
    
    log_success "Build process completed successfully!"
    
    if [[ "$INSTALL_SYSTEM" == "true" ]]; then
        log_info "To enable health monitoring:"
        log_info "  systemctl enable --now lsm_hide_health.timer"
    fi
}

main "$@"
