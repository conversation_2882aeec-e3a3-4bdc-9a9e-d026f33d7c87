[Unit]
Description=LSM Hide Health Check Service
Documentation=man:systemd.service(5)
After=network.target
Wants=network.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/lsm_hide_health_check.sh check
User=root
Group=root
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/sys/fs/bpf /proc /var/log /var/run
PrivateTmp=true
PrivateDevices=false
ProtectKernelTunables=false
ProtectKernelModules=false
ProtectControlGroups=false
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=false
RestrictNamespaces=true
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

[Install]
WantedBy=multi-user.target
