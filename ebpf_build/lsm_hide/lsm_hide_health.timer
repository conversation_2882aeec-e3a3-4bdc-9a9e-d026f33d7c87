[Unit]
Description=LSM Hide Health Check Timer
Documentation=man:systemd.timer(5)
Requires=lsm_hide_health.service

[Timer]
# Run health check every 5 minutes
OnCalendar=*:0/5
# Run immediately on boot
OnBootSec=30sec
# Randomize start time by up to 30 seconds to avoid thundering herd
RandomizedDelaySec=30sec
# Ensure timer is persistent across reboots
Persistent=true

[Install]
WantedBy=timers.target
