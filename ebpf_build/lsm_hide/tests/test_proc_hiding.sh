#!/bin/bash
#
# test_proc_hiding.sh - Test Suite for Proc Filesystem Protection
#
# This script validates that hidden PIDs are properly concealed from
# various system tools and commands.
#
# Compatible with kernel 6.8.0-1026-azure

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARENT_DIR="$(dirname "$SCRIPT_DIR")"
LOADER_BIN="${PARENT_DIR}/output/loader_lsm_hide"
HEALTH_CHECK="${PARENT_DIR}/health_check.sh"
TEST_LOG="/tmp/lsm_hide_test.log"
PERFORMANCE_LOG="/tmp/lsm_hide_perf.log"

# Test configuration
TEST_TIMEOUT=30
PERFORMANCE_ITERATIONS=1000
EXPECTED_OVERHEAD_PERCENT=5

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Logging functions
log_test() {
    echo -e "${BLUE}[TEST]${NC} $*" | tee -a "$TEST_LOG"
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $*" | tee -a "$TEST_LOG"
    ((TESTS_PASSED++))
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $*" | tee -a "$TEST_LOG"
    ((TESTS_FAILED++))
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$TEST_LOG"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$TEST_LOG"
}

# Test helper functions
start_test() {
    local test_name="$1"
    ((TESTS_TOTAL++))
    log_test "Starting: $test_name"
}

# Create test process that will be hidden
create_test_process() {
    local sleep_duration=${1:-60}
    
    # Create a simple background process
    sleep "$sleep_duration" &
    local pid=$!
    echo "$pid"
}

# Add PID to hidden list
add_hidden_pid() {
    local pid="$1"
    
    if [[ -e "/proc/proc_hide_fallback" ]]; then
        # Use kernel module interface
        echo "add $pid" > /proc/proc_hide_fallback
        log_info "Added PID $pid to hidden list via kernel module"
    else
        # Use eBPF loader (would need to restart with PID)
        log_info "Would add PID $pid to eBPF hidden list"
    fi
}

# Remove PID from hidden list
remove_hidden_pid() {
    local pid="$1"
    
    if [[ -e "/proc/proc_hide_fallback" ]]; then
        # Clear all hidden PIDs (simplified)
        echo "clear" > /proc/proc_hide_fallback
        log_info "Cleared hidden PID list"
    fi
}

# Test 1: Basic PID hiding with ps command
test_ps_hiding() {
    start_test "PS command PID hiding"
    
    local test_pid=$(create_test_process 30)
    
    # Verify process is visible before hiding
    if ps -p "$test_pid" >/dev/null 2>&1; then
        log_info "Test process $test_pid is visible before hiding"
    else
        log_fail "Test process $test_pid not found before hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    fi
    
    # Hide the process
    add_hidden_pid "$test_pid"
    sleep 2
    
    # Test if process is hidden from ps
    if ps -p "$test_pid" >/dev/null 2>&1; then
        log_fail "Process $test_pid still visible in ps after hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    else
        log_pass "Process $test_pid successfully hidden from ps"
    fi
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Test 2: /proc directory listing
test_proc_listing() {
    start_test "/proc directory listing"
    
    local test_pid=$(create_test_process 30)
    
    # Verify process directory exists before hiding
    if [[ -d "/proc/$test_pid" ]]; then
        log_info "Process directory /proc/$test_pid exists before hiding"
    else
        log_fail "Process directory /proc/$test_pid not found before hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    fi
    
    # Hide the process
    add_hidden_pid "$test_pid"
    sleep 2
    
    # Test if process directory is hidden
    if [[ -d "/proc/$test_pid" ]]; then
        log_fail "Process directory /proc/$test_pid still visible after hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    else
        log_pass "Process directory /proc/$test_pid successfully hidden"
    fi
    
    # Test ls /proc
    if ls /proc/ | grep -q "^$test_pid$"; then
        log_fail "Process $test_pid visible in 'ls /proc' after hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    else
        log_pass "Process $test_pid hidden from 'ls /proc'"
    fi
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Test 3: find command
test_find_command() {
    start_test "find command PID hiding"
    
    local test_pid=$(create_test_process 30)
    
    # Hide the process
    add_hidden_pid "$test_pid"
    sleep 2
    
    # Test find command
    if find /proc -maxdepth 1 -name "$test_pid" -type d 2>/dev/null | grep -q "$test_pid"; then
        log_fail "Process $test_pid found by 'find /proc' after hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    else
        log_pass "Process $test_pid hidden from 'find /proc'"
    fi
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Test 4: pgrep and pidof commands
test_process_search_tools() {
    start_test "pgrep and pidof commands"
    
    # Create a process with a unique name
    sleep 300 &
    local test_pid=$!
    local process_name="sleep"
    
    # Hide the process
    add_hidden_pid "$test_pid"
    sleep 2
    
    # Test pgrep
    if pgrep -f "sleep 300" | grep -q "$test_pid"; then
        log_fail "Process $test_pid found by pgrep after hiding"
        kill "$test_pid" 2>/dev/null || true
        return 1
    else
        log_pass "Process $test_pid hidden from pgrep"
    fi
    
    # Test pidof (if available)
    if command -v pidof >/dev/null 2>&1; then
        if pidof sleep | grep -q "$test_pid"; then
            log_fail "Process $test_pid found by pidof after hiding"
            kill "$test_pid" 2>/dev/null || true
            return 1
        else
            log_pass "Process $test_pid hidden from pidof"
        fi
    else
        log_warn "pidof command not available, skipping test"
    fi
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Test 5: htop and top commands (if available)
test_system_monitors() {
    start_test "System monitoring tools"
    
    local test_pid=$(create_test_process 30)
    
    # Hide the process
    add_hidden_pid "$test_pid"
    sleep 2
    
    # Test with top (batch mode)
    if command -v top >/dev/null 2>&1; then
        if top -b -n 1 | grep -q "$test_pid"; then
            log_fail "Process $test_pid visible in top after hiding"
            kill "$test_pid" 2>/dev/null || true
            return 1
        else
            log_pass "Process $test_pid hidden from top"
        fi
    else
        log_warn "top command not available, skipping test"
    fi
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Performance Test: Measure getdents64 syscall overhead
test_performance_overhead() {
    start_test "Performance overhead measurement"
    
    log_info "Measuring baseline performance..."
    
    # Baseline measurement (without hiding)
    local baseline_time
    baseline_time=$(time_getdents64_operations "$PERFORMANCE_ITERATIONS")
    
    log_info "Baseline time: ${baseline_time}ms"
    
    # Create and hide a test process
    local test_pid=$(create_test_process 60)
    add_hidden_pid "$test_pid"
    sleep 2
    
    log_info "Measuring performance with hiding enabled..."
    
    # Measurement with hiding enabled
    local hiding_time
    hiding_time=$(time_getdents64_operations "$PERFORMANCE_ITERATIONS")
    
    log_info "Hiding enabled time: ${hiding_time}ms"
    
    # Calculate overhead
    local overhead_percent
    overhead_percent=$(echo "scale=2; (($hiding_time - $baseline_time) / $baseline_time) * 100" | bc -l)
    
    log_info "Performance overhead: ${overhead_percent}%"
    
    # Check if overhead is within acceptable limits
    if (( $(echo "$overhead_percent <= $EXPECTED_OVERHEAD_PERCENT" | bc -l) )); then
        log_pass "Performance overhead (${overhead_percent}%) within acceptable limit (${EXPECTED_OVERHEAD_PERCENT}%)"
    else
        log_fail "Performance overhead (${overhead_percent}%) exceeds limit (${EXPECTED_OVERHEAD_PERCENT}%)"
    fi
    
    # Log to performance file
    echo "$(date): Baseline=${baseline_time}ms, Hiding=${hiding_time}ms, Overhead=${overhead_percent}%" >> "$PERFORMANCE_LOG"
    
    # Cleanup
    remove_hidden_pid "$test_pid"
    kill "$test_pid" 2>/dev/null || true
}

# Helper function to time getdents64 operations
time_getdents64_operations() {
    local iterations="$1"
    local start_time end_time duration
    
    start_time=$(date +%s%3N)
    
    for ((i=0; i<iterations; i++)); do
        ls /proc >/dev/null 2>&1
    done
    
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    
    echo "$duration"
}

# Test system requirements
test_system_requirements() {
    start_test "System requirements check"
    
    # Check kernel version
    local kernel_version=$(uname -r)
    log_info "Kernel version: $kernel_version"
    
    if [[ "$kernel_version" == "6.8.0-1026-azure" ]]; then
        log_pass "Kernel version matches target"
    else
        log_warn "Kernel version differs from target (6.8.0-1026-azure)"
    fi
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        log_pass "Running as root"
    else
        log_fail "Not running as root"
        return 1
    fi
    
    # Check required tools
    local required_tools=("ps" "ls" "find" "bc")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            log_pass "Required tool '$tool' available"
        else
            log_fail "Required tool '$tool' not available"
            return 1
        fi
    done
}

# Main test runner
run_all_tests() {
    log_info "=== LSM Hide Test Suite ==="
    log_info "Starting comprehensive testing..."
    
    # Initialize log files
    echo "LSM Hide Test Log - $(date)" > "$TEST_LOG"
    echo "LSM Hide Performance Log - $(date)" > "$PERFORMANCE_LOG"
    
    # Run tests
    test_system_requirements
    test_ps_hiding
    test_proc_listing
    test_find_command
    test_process_search_tools
    test_system_monitors
    test_performance_overhead
    
    # Summary
    log_info "=== Test Summary ==="
    log_info "Total tests: $TESTS_TOTAL"
    log_info "Passed: $TESTS_PASSED"
    log_info "Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_pass "All tests passed!"
        return 0
    else
        log_fail "$TESTS_FAILED tests failed"
        return 1
    fi
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    echo "This test suite must be run as root"
    exit 1
fi

# Check if bc is available for calculations
if ! command -v bc >/dev/null 2>&1; then
    echo "bc (calculator) is required for performance tests"
    echo "Install with: apt-get install bc"
    exit 1
fi

# Run tests
run_all_tests
